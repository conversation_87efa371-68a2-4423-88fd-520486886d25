'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Sparkles, ShoppingBag, Users, Clock, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

export function HeroSection() {
  const { t, isRTL } = useLanguage();
  const [currentStats, setCurrentStats] = useState({
    stores: 0,
    time: 0,
    success: 0
  });

  useEffect(() => {
    // Animate statistics
    const targetStats = { stores: 1247, time: 4.5, success: 96 };
    const duration = 2000;
    const steps = 60;
    const stepDuration = duration / steps;

    let step = 0;
    const interval = setInterval(() => {
      step++;
      const progress = step / steps;
      
      setCurrentStats({
        stores: Math.floor(targetStats.stores * progress),
        time: Math.round(targetStats.time * progress * 10) / 10,
        success: Math.floor(targetStats.success * progress)
      });

      if (step >= steps) {
        clearInterval(interval);
        setCurrentStats(targetStats);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center pt-16 bg-gradient-to-br from-green-50/50 via-white to-blue-50/50">
      {/* Simplified Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full filter blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          {/* Content */}
          <div className="space-y-8 max-w-4xl mx-auto">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-green-200 mx-auto">
                <Sparkles className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-700">
                  {t('hero.badge')}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                {t('hero.title')}
                <span className="block text-green-600 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mt-2">
                  {t('hero.subtitle')}
                </span>
              </h1>

              <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
                {t('hero.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-10 py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Sparkles className="h-5 w-5 mr-2" />
                {t('hero.cta1')}
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="px-10 py-4 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-green-500 hover:text-green-600 transition-all duration-200"
              >
                <Play className="h-5 w-5 mr-2" />
                {t('hero.cta2')}
              </Button>
            </div>

            {/* Simplified Trust Badges */}
            <div className="flex flex-wrap justify-center gap-6 py-6">
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/60 px-3 py-2 rounded-full">
                <Sparkles className="h-4 w-4 text-green-500" />
                {t('hero.trustBadges.aiPowered')}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/60 px-3 py-2 rounded-full">
                <ShoppingBag className="h-4 w-4 text-blue-500" />
                {t('hero.trustBadges.localPayments')}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/60 px-3 py-2 rounded-full">
                <Users className="h-4 w-4 text-purple-500" />
                {t('hero.trustBadges.multiLanguage')}
              </div>
            </div>

            {/* Key Statistics - Simplified */}
            <div className="grid grid-cols-3 gap-6 pt-6 mt-6 border-t border-gray-200/50 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
                  {currentStats.stores.toLocaleString()}+
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.stores')}</p>
              </div>

              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-green-600 mb-1">
                  {currentStats.time}min
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.time')}</p>
              </div>

              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-1">
                  {currentStats.success}%
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.success')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}