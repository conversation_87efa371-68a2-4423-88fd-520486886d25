'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Sparkles, ShoppingBag, Users, Clock, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

export function HeroSection() {
  const { t, isRTL } = useLanguage();
  const [currentStats, setCurrentStats] = useState({
    stores: 0,
    time: 0,
    success: 0
  });

  useEffect(() => {
    // Animate statistics
    const targetStats = { stores: 1247, time: 4.5, success: 96 };
    const duration = 2000;
    const steps = 60;
    const stepDuration = duration / steps;

    let step = 0;
    const interval = setInterval(() => {
      step++;
      const progress = step / steps;
      
      setCurrentStats({
        stores: Math.floor(targetStats.stores * progress),
        time: Math.round(targetStats.time * progress * 10) / 10,
        success: Math.floor(targetStats.success * progress)
      });

      if (step >= steps) {
        clearInterval(interval);
        setCurrentStats(targetStats);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-green-50 via-blue-50 to-orange-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-72 h-72 bg-green-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-72 h-72 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className={`space-y-8 ${isRTL ? 'lg:order-2' : ''}`}>
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-green-200">
                <Sparkles className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-700">
                  {t('hero.badge')}
                </span>
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                {t('hero.title')}
                <span className="block text-green-600 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  {t('hero.subtitle')}
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                {t('hero.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-6 text-lg font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <Sparkles className="h-5 w-5 mr-2" />
                {t('hero.cta1')}
              </Button>
              
              <Button 
                variant="outline" 
                size="lg"
                className="px-8 py-6 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-gray-400 transition-all duration-300 hover:shadow-lg"
              >
                <Play className="h-5 w-5 mr-2" />
                {t('hero.cta2')}
              </Button>
            </div>

            {/* Trust Badges */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-6">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                {t('hero.trustBadges.aiPowered')}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                {t('hero.trustBadges.localPayments')}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                {t('hero.trustBadges.mobileOptimized')}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                {t('hero.trustBadges.multiLanguage')}
              </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <ShoppingBag className="h-6 w-6 text-green-600 mr-2" />
                  <span className="text-3xl font-bold text-gray-900">
                    {currentStats.stores.toLocaleString()}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.stores')}</p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="h-6 w-6 text-blue-600 mr-2" />
                  <span className="text-3xl font-bold text-gray-900">
                    {currentStats.time}min
                  </span>
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.time')}</p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-6 w-6 text-orange-600 mr-2" />
                  <span className="text-3xl font-bold text-gray-900">
                    {currentStats.success}%
                  </span>
                </div>
                <p className="text-sm text-gray-600">{t('hero.stats.success')}</p>
              </div>
            </div>
          </div>

          {/* Visual Demo */}
          <div className={`relative ${isRTL ? 'lg:order-1' : ''}`}>
            <div className="relative bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
              <div className="space-y-6">
                {/* Mock Browser Header */}
                <div className="flex items-center gap-2 pb-4 border-b border-gray-200">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm text-gray-600 text-center">
                    boutique-amina.sharyou.dz
                  </div>
                </div>

                {/* Mock Store Preview */}
                <div className="space-y-4">
                  <div className="h-32 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800">Artisanat Amina</h3>
                      <p className="text-sm text-gray-600">Créé par IA en 3 minutes</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                      <ShoppingBag className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                      <Users className="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* AI Badge */}
                <div className="absolute -top-4 -right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                  <Sparkles className="h-4 w-4 inline mr-1" />
                  AI Generated
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-8 -left-8 bg-green-500 text-white p-3 rounded-xl shadow-lg animate-bounce">
              <TrendingUp className="h-6 w-6" />
            </div>
            <div className="absolute -bottom-8 -right-8 bg-blue-500 text-white p-3 rounded-xl shadow-lg animate-pulse">
              <Sparkles className="h-6 w-6" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}