import React from 'react';

interface SharyouIconProps {
  className?: string;
  size?: number;
}

export function SharyouIcon({ className = "", size = 24 }: SharyouIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Fond circulaire avec gradient */}
      <defs>
        <linearGradient id="sharyou-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#0ea5e9" />
          <stop offset="100%" stopColor="#f37316" />
        </linearGradient>
      </defs>

      {/* Cercle principal */}
      <circle
        cx="16"
        cy="16"
        r="15"
        fill="url(#sharyou-gradient)"
        stroke="none"
      />

      {/* Panier d'achat classique */}
      {/* Poignée du panier */}
      <path
        d="M6 8 L8 8 L10 20 L24 20 L26 12 L12 12"
        stroke="white"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />

      {/* Corps du panier */}
      <path
        d="M10 20 L24 20 L26 12 L12 12 L10 20 Z"
        fill="white"
        opacity="0.9"
      />

      {/* Lignes de produits dans le panier */}
      <line x1="14" y1="14.5" x2="22" y2="14.5" stroke="#0ea5e9" strokeWidth="1.5" strokeLinecap="round" />
      <line x1="14" y1="16.5" x2="20" y2="16.5" stroke="#0ea5e9" strokeWidth="1.5" strokeLinecap="round" />
      <line x1="14" y1="18.5" x2="18" y2="18.5" stroke="#0ea5e9" strokeWidth="1.5" strokeLinecap="round" />

      {/* Roues du panier */}
      <circle cx="12" cy="24" r="2" fill="white" />
      <circle cx="22" cy="24" r="2" fill="white" />

      {/* Centre des roues */}
      <circle cx="12" cy="24" r="0.8" fill="#0ea5e9" />
      <circle cx="22" cy="24" r="0.8" fill="#0ea5e9" />
    </svg>
  );
}

// Version simplifiée pour les petites tailles
export function SharyouIconSimple({ className = "", size = 16 }: SharyouIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        <linearGradient id="simple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#0ea5e9" />
          <stop offset="100%" stopColor="#f37316" />
        </linearGradient>
      </defs>

      <circle
        cx="12"
        cy="12"
        r="11"
        fill="url(#simple-gradient)"
      />

      {/* Panier d'achat simplifié */}
      {/* Poignée et corps du panier */}
      <path
        d="M5 7 L6.5 7 L8 16 L17 16 L18 10 L9 10"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />

      {/* Corps du panier rempli */}
      <path
        d="M8 16 L17 16 L18 10 L9 10 L8 16 Z"
        fill="white"
        opacity="0.8"
      />

      {/* Lignes de produits */}
      <line x1="10" y1="11.5" x2="15.5" y2="11.5" stroke="#0ea5e9" strokeWidth="1" strokeLinecap="round" />
      <line x1="10" y1="13" x2="14.5" y2="13" stroke="#0ea5e9" strokeWidth="1" strokeLinecap="round" />

      {/* Roues simplifiées */}
      <circle cx="10" cy="18.5" r="1.5" fill="white" />
      <circle cx="15" cy="18.5" r="1.5" fill="white" />
      <circle cx="10" cy="18.5" r="0.6" fill="#0ea5e9" />
      <circle cx="15" cy="18.5" r="0.6" fill="#0ea5e9" />
    </svg>
  );
}
