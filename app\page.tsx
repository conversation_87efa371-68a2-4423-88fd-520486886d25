'use client';

import { Navigation } from '@/components/navigation';
import { HeroSection } from '@/components/hero-section';
import { AIBuilderSection } from '@/components/ai-builder-section';
import { WhyChooseSection } from '@/components/why-choose-section';
import { TestimonialsSection } from '@/components/testimonials-section';
import { Footer } from '@/components/footer';
import { ScrollToTop } from '@/components/scroll-to-top';
import { LanguageProvider } from '@/hooks/use-language';

export default function Home() {
  return (
    <LanguageProvider>
      <div className="min-h-screen">
        <Navigation />
        <main>
          <HeroSection />
          <section id="ai-builder">
            <AIBuilderSection />
          </section>
          <section id="why-choose">
            <WhyChooseSection />
          </section>
          <section id="testimonials">
            <TestimonialsSection />
          </section>
        </main>
        <footer id="footer">
          <Footer />
        </footer>
        <ScrollToTop />
      </div>
    </LanguageProvider>
  );
}