import './globals.css';
import type { Metadata } from 'next';
import { Inter, Cairo, Poppins } from 'next/font/google';
import { AuthProvider } from '@/providers/auth-provider';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
});

const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700', '800'],
});

const cairo = Cairo({
  subsets: ['latin', 'arabic'],
  variable: '--font-cairo',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
});

export const metadata: Metadata = {
  title: 'Sharyou - Créez votre boutique e-commerce avec l\'IA | Plateforme Algérienne',
  description: 'Première plateforme e-commerce algérienne alimentée par l\'IA<PERSON>, personnalisez et lancez votre boutique en ligne en 5 minutes sans compétences techniques.',
  keywords: 'e-commerce, Algérie, boutique en ligne, IA, intelligence artificielle, vente en ligne, Sharyou',
  authors: [{ name: 'Sharyou Team' }],
  creator: 'Sharyou',
  publisher: 'Sharyou',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://sharyou.dz'),
  alternates: {
    canonical: '/',
    languages: {
      'fr-DZ': '/fr',
      'ar-DZ': '/ar',
    },
  },
  openGraph: {
    title: 'Sharyou - Créez votre boutique e-commerce avec l\'IA',
    description: 'Première plateforme e-commerce algérienne alimentée par l\'IA. Créez votre boutique en 5 minutes.',
    url: 'https://sharyou.dz',
    siteName: 'Sharyou',
    locale: 'fr_DZ',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Sharyou - Plateforme e-commerce IA pour l\'Algérie',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sharyou - Créez votre boutique e-commerce avec l\'IA',
    description: 'Première plateforme e-commerce algérienne alimentée par l\'IA.',
    images: ['/og-image.jpg'],
    creator: '@sharyou_dz',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr" className={`${inter.variable} ${poppins.variable} ${cairo.variable}`}>
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicon.svg" />
        <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon.svg" />
        <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon.svg" />
        <link rel="manifest" href="/site.webmanifest" />

        {/* Préchargement des polices pour de meilleures performances */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Sharyou" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body className={`${inter.className} antialiased bg-white text-neutral-900`}>
        <AuthProvider>
          {children}
        </AuthProvider>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "Sharyou",
              "description": "Première plateforme e-commerce algérienne alimentée par l'IA",
              "url": "https://sharyou.dz",
              "applicationCategory": "BusinessApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "DZD"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "reviewCount": "247"
              }
            })
          }}
        />
      </body>
    </html>
  );
}