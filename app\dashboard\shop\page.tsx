'use client';

import { motion } from 'framer-motion';
import {
  Store,
  Settings,
  Palette,
  Globe,
  Eye,
  Edit,
  Copy,
  ExternalLink,
  Smartphone,
  Monitor,
  Tablet,
  Plus,
  Image,
  Type,
  Layout,
  Zap,
  Package
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { ShopGenerationDialog } from '@/components/shop/shop-generation-dialog';

export default function ShopPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showGenerationDialog, setShowGenerationDialog] = useState(false);
  const [hasShop, setHasShop] = useState(false); // Simule l'état de la boutique

  // Vérifier si l'utilisateur a déjà une boutique au chargement
  useEffect(() => {
    // Simuler la vérification de l'existence d'une boutique
    // Dans un vrai projet, ceci ferait un appel API
    const checkShopExists = () => {
      const shopExists = localStorage.getItem('user_has_shop') === 'true';
      setHasShop(shopExists);
      // Ne plus afficher automatiquement la boîte de dialogue
    };

    checkShopExists();
  }, []);

  const shopStats = [
    { label: 'Visiteurs aujourd\'hui', value: '0', icon: Eye, color: 'blue' },
    { label: 'Ventes ce mois', value: '0 DA', icon: Store, color: 'green' },
    { label: 'Produits en ligne', value: '0', icon: Package, color: 'purple' },
    { label: 'Taux de conversion', value: '0%', icon: Zap, color: 'orange' },
  ];

  const customizationOptions = [
    {
      title: 'Thème et couleurs',
      description: 'Personnalisez l\'apparence de votre boutique',
      icon: Palette,
      action: 'Personnaliser'
    },
    {
      title: 'Logo et images',
      description: 'Ajoutez votre logo et images de marque',
      icon: Image,
      action: 'Modifier'
    },
    {
      title: 'Mise en page',
      description: 'Organisez la structure de votre boutique',
      icon: Layout,
      action: 'Configurer'
    },
    {
      title: 'Contenu et textes',
      description: 'Modifiez les textes et descriptions',
      icon: Type,
      action: 'Éditer'
    },
  ];

  const handleGenerateShop = (prompt: string) => {
    console.log('Génération de la boutique avec le prompt:', prompt);
    // Ici, vous feriez l'appel API pour générer la boutique

    // Simuler la création de la boutique
    localStorage.setItem('user_has_shop', 'true');
    localStorage.setItem('shop_generation_prompt', prompt);
    setHasShop(true);

    // Optionnel: afficher une notification de succès
    alert('Boutique générée avec succès !');
  };

  const handleCloseDialog = () => {
    setShowGenerationDialog(false);
    // Ne pas marquer automatiquement comme ayant une boutique
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <Button
              variant={activeTab === 'overview' ? 'default' : 'outline'}
              onClick={() => setActiveTab('overview')}
              className={activeTab === 'overview' ? 'bg-brand-500 hover:bg-brand-600' : ''}
            >
              <Store className="w-4 h-4 mr-2" />
              Aperçu
            </Button>
            <Button
              variant={activeTab === 'customize' ? 'default' : 'outline'}
              onClick={() => setActiveTab('customize')}
              className={activeTab === 'customize' ? 'bg-brand-500 hover:bg-brand-600' : ''}
            >
              <Palette className="w-4 h-4 mr-2" />
              Personnaliser
            </Button>
            <Button
              variant={activeTab === 'settings' ? 'default' : 'outline'}
              onClick={() => setActiveTab('settings')}
              className={activeTab === 'settings' ? 'bg-brand-500 hover:bg-brand-600' : ''}
            >
              <Settings className="w-4 h-4 mr-2" />
              Paramètres
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
            <Eye className="w-4 h-4 mr-2" />
            Prévisualiser
          </Button>
          <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
            <Globe className="w-4 h-4 mr-2" />
            Publier
          </Button>
        </div>
      </motion.div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Shop Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {shopStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-neutral-900 mt-1">{stat.value}</p>
                  </div>
                  <div className={`w-12 h-12 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Shop Preview */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-neutral-200"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <div className="p-6 border-b border-neutral-100">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-neutral-900">Aperçu de votre boutique</h3>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    <Monitor className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Tablet className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Smartphone className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="p-6">
              {!hasShop ? (
                <div className="bg-neutral-50 rounded-lg border-2 border-dashed border-neutral-200 h-96 flex items-center justify-center">
                  <div className="text-center">
                    <Store className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
                    <h4 className="text-lg font-semibold text-neutral-700 mb-2">Votre boutique n'est pas encore configurée</h4>
                    <p className="text-neutral-500 mb-6 max-w-md">
                      Commencez par créer votre boutique avec l'IA ou personnalisez-la manuellement
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button
                        onClick={() => setShowGenerationDialog(true)}
                        className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Créer avec l'IA
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setActiveTab('customize')}
                        className="border-brand-200 text-brand-600 hover:bg-brand-50"
                      >
                        <Palette className="w-4 h-4 mr-2" />
                        Personnaliser manuellement
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-lg border border-brand-200 h-96 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Store className="w-10 h-10 text-white" />
                    </div>
                    <h4 className="text-xl font-semibold text-neutral-900 mb-2">Votre boutique est prête !</h4>
                    <p className="text-neutral-600 mb-6 max-w-md">
                      Votre boutique a été générée avec succès. Vous pouvez maintenant la personnaliser et ajouter vos produits.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
                        <Eye className="w-4 h-4 mr-2" />
                        Voir ma boutique
                      </Button>
                      <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
                        <Plus className="w-4 h-4 mr-2" />
                        Ajouter des produits
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-neutral-200"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className="p-6 border-b border-neutral-100">
              <h3 className="text-lg font-semibold text-neutral-900">Actions rapides</h3>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-brand-50 hover:border-brand-300">
                  <Plus className="w-6 h-6 text-brand-600" />
                  <span className="font-medium">Ajouter un produit</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-brand-50 hover:border-brand-300">
                  <Palette className="w-6 h-6 text-brand-600" />
                  <span className="font-medium">Changer le thème</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-brand-50 hover:border-brand-300">
                  <Globe className="w-6 h-6 text-brand-600" />
                  <span className="font-medium">Configurer le domaine</span>
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Customize Tab */}
      {activeTab === 'customize' && (
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {customizationOptions.map((option, index) => (
              <motion.div
                key={option.title}
                className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center">
                      <option.icon className="w-6 h-6 text-brand-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-neutral-900 mb-1">{option.title}</h4>
                      <p className="text-sm text-neutral-600">{option.description}</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="border-brand-200 text-brand-600 hover:bg-brand-50">
                    {option.action}
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white rounded-xl shadow-sm border border-neutral-200">
            <div className="p-6 border-b border-neutral-100">
              <h3 className="text-lg font-semibold text-neutral-900">Paramètres de la boutique</h3>
            </div>
            
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Nom de la boutique
                  </label>
                  <input
                    type="text"
                    placeholder="Ma Boutique Sharyou"
                    className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Description
                  </label>
                  <textarea
                    rows={3}
                    placeholder="Décrivez votre boutique..."
                    className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    URL de la boutique
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-neutral-200 bg-neutral-50 text-neutral-500 text-sm">
                      sharyou.com/
                    </span>
                    <input
                      type="text"
                      placeholder="ma-boutique"
                      className="flex-1 px-4 py-3 border border-neutral-200 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
                    Sauvegarder les paramètres
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Shop Generation Dialog */}
      <ShopGenerationDialog
        isOpen={showGenerationDialog}
        onClose={handleCloseDialog}
        onGenerate={handleGenerateShop}
      />
    </div>
  );
}
