'use client';

import { motion } from 'framer-motion';
import { Package, Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Inbox, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';

const products: any[] = [];

export default function ProductsPage() {
  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <motion.div
        className="flex justify-end mb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 shadow-lg hover:shadow-xl transition-all duration-200">
          <Plus className="w-4 h-4 mr-2" />
          Ajouter un produit
        </Button>
      </motion.div>

      {/* Filters */}
      <motion.div
        className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Rechercher un produit..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filtres
          </Button>
        </div>
      </motion.div>

      {/* Products Table */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {products.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Inbox className="w-10 h-10 text-neutral-400" />
            </div>
            <h3 className="text-lg font-semibold text-neutral-900 mb-2">Aucun produit dans votre catalogue</h3>
            <p className="text-neutral-500 mb-6 max-w-md mx-auto">
              Commencez par ajouter vos premiers produits pour démarrer votre boutique en ligne
            </p>
            <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
              <Plus className="w-4 h-4 mr-2" />
              Ajouter votre premier produit
            </Button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-neutral-50 border-b border-neutral-200">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Produit</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Catégorie</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Prix</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Stock</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Statut</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {products.map((product, index) => (
                    <motion.tr
                      key={product.id}
                      className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-neutral-200 rounded-lg flex items-center justify-center">
                            <Package className="w-6 h-6 text-neutral-400" />
                          </div>
                          <div>
                            <p className="font-medium text-neutral-900">{product.name}</p>
                            <p className="text-sm text-neutral-500">ID: {product.id}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-neutral-700">{product.category}</td>
                      <td className="py-4 px-6 font-medium text-neutral-900">{product.price}</td>
                      <td className="py-4 px-6">
                        <span className={`font-medium ${product.stock === 0 ? 'text-red-600' : product.stock < 10 ? 'text-orange-600' : 'text-emerald-600'}`}>
                          {product.stock}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          product.status === 'Actif' ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
                        }`}>
                          {product.status}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" className="hover:bg-blue-50 hover:text-blue-600">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="hover:bg-red-50 hover:text-red-600">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="hover:bg-neutral-100">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between p-6 border-t border-neutral-100 bg-neutral-50">
              <p className="text-sm text-neutral-600">
                Affichage de 0 à 0 sur 0 produits
              </p>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  Précédent
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Suivant
                </Button>
              </div>
            </div>
          </>
        )}
      </motion.div>
    </div>
  );
}
