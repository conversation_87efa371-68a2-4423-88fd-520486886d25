'use client';

import {
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  Sparkles,
  Youtube,
  Clock,
  Shield,
  Send
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/use-language';

export function Footer() {
  const { t, isRTL } = useLanguage();

  const socialLinks = [
    { icon: Facebook, href: '#', label: t('footer.social.facebook') },
    { icon: Instagram, href: '#', label: t('footer.social.instagram') },
    { icon: Linkedin, href: '#', label: t('footer.social.linkedin') },
    { icon: Youtube, href: '#', label: t('footer.social.youtube') },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer */}
        <div className="py-16">
          <div className="grid lg:grid-cols-5 md:grid-cols-2 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <span className="text-2xl font-bold">Sharyou</span>
              </div>

              <p className="text-gray-300 leading-relaxed max-w-md">
                {t('footer.description')}
              </p>

              <p className="text-green-400 font-medium">
                {t('footer.mission')}
              </p>

              {/* Newsletter */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white">
                  {t('footer.newsletter.title')}
                </h4>
                <p className="text-gray-400 text-sm">
                  {t('footer.newsletter.description')}
                </p>
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder={t('footer.newsletter.placeholder')}
                    className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"
                  />
                  <Button className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex gap-4">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center hover:bg-gradient-to-br hover:from-green-500 hover:to-blue-500 transition-all duration-300 transform hover:scale-110"
                    aria-label={social.label}
                    title={social.label}
                  >
                    <social.icon className="h-5 w-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Product Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">
                {t('footer.links.product')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.features')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.templates')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.pricing')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.enterprise')}
                  </a>
                </li>
              </ul>
            </div>

            {/* Support Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">
                {t('footer.links.support')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.help')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.contact')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.tutorials')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.webinars')}
                  </a>
                </li>
              </ul>
            </div>

            {/* Resources Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">
                Ressources
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.blog')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.success')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                    {t('footer.links.security')}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Contact Info */}
          <div className="mt-16 pt-8 border-t border-gray-800">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                  <Mail className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Email</p>
                  <p className="text-white"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                  <Phone className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Téléphone</p>
                  <p className="text-white">+213 (0) 21 XX XX XX</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Adresse</p>
                  <p className="text-white">Alger, Algérie</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-sm">
              {t('footer.copyright')}
            </p>
            
            <div className="flex gap-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-200">
                {t('footer.links.privacy')}
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-200">
                {t('footer.links.terms')}
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-200">
                {t('footer.links.cookies')}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}