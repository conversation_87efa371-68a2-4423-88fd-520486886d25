'use client';

import { forwardRef, InputHTMLAttributes, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FormFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  icon?: ReactNode;
  helperText?: string;
  showError?: boolean;
}

export const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  ({ 
    label, 
    error, 
    icon, 
    helperText, 
    showError = true, 
    className, 
    type = 'text',
    ...props 
  }, ref) => {
    const hasError = error && showError;

    return (
      <motion.div
        className="space-y-2"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* Label */}
        <motion.label
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-neutral-700"
          initial={{ x: -10, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {label}
          {props.required && (
            <motion.span
              className="text-brand-500 ml-1"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2, delay: 0.2 }}
            >
              *
            </motion.span>
          )}
        </motion.label>

        {/* Input Container */}
        <motion.div
          className="relative"
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          {/* Icon */}
          {icon && (
            <motion.div
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.4, delay: 0.3 }}
            >
              {icon}
            </motion.div>
          )}

          {/* Input */}
          <motion.input
            ref={ref}
            type={type}
            className={cn(
              // Base styles
              "w-full px-4 py-3 rounded-lg border transition-all duration-200",
              "placeholder:text-neutral-400 text-neutral-900",
              "focus:outline-none focus:ring-2 focus:ring-brand-500/20",

              // Icon padding
              icon && "pl-11",

              // States
              hasError
                ? "border-red-300 focus:border-red-500 bg-red-50/50"
                : "border-neutral-200 focus:border-brand-500 bg-white hover:border-neutral-300",

              // Disabled state
              props.disabled && "opacity-50 cursor-not-allowed bg-neutral-50",

              className
            )}
            whileFocus={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
            {...props}
          />
        </motion.div>

        {/* Helper Text or Error */}
        {(hasError || helperText) && (
          <motion.div
            className="min-h-[1.25rem]"
            initial={{ y: -10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {hasError ? (
              <motion.p
                className="text-sm text-red-600 flex items-center gap-1"
                initial={{ x: -10 }}
                animate={{ x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <motion.svg
                  className="w-4 h-4 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  initial={{ rotate: -90, scale: 0 }}
                  animate={{ rotate: 0, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </motion.svg>
                {error}
              </motion.p>
            ) : helperText ? (
              <motion.p
                className="text-sm text-neutral-500"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                {helperText}
              </motion.p>
            ) : null}
          </motion.div>
        )}
      </motion.div>
    );
  }
);

FormField.displayName = 'FormField';
