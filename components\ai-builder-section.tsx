'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Brain,
  MessageSquare,
  Rocket,
  Check,
  Play,
  Smartphone,
  CreditCard,
  Globe,
  ArrowRight,
  TrendingUp
} from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

export function AIBuilderSection() {
  const { t, isRTL } = useLanguage();
  const [activeStep, setActiveStep] = useState(0);
  
  const steps = t('aiBuilder.steps');
  const features = t('aiBuilder.features.list');

  const stepIcons = [Brain, MessageSquare, Rocket];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('aiBuilder.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
            {t('aiBuilder.subtitle')}
          </p>
          <p className="text-lg text-gray-500 max-w-4xl mx-auto">
            {t('aiBuilder.description')}
          </p>
        </div>

        {/* Video Demo Section */}
        <div className="mb-20">
          <div className="relative max-w-4xl mx-auto">
            <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden shadow-2xl">
              <div className="aspect-video bg-gradient-to-br from-green-400 via-blue-500 to-purple-600 flex flex-col items-center justify-center text-center px-8">
                <div className="text-white mb-6">
                  <h3 className="text-2xl font-bold mb-2">{t('aiBuilder.videoTitle')}</h3>
                  <p className="text-lg opacity-90">{t('aiBuilder.videoDescription')}</p>
                </div>
                <Button
                  size="lg"
                  className="bg-white/20 backdrop-blur-sm text-white border-2 border-white/30 hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
                >
                  <Play className="h-8 w-8 mr-3" />
                  <span className="text-lg font-semibold">{t('aiBuilder.videoTitle')}</span>
                </Button>
              </div>
              
              {/* Video overlay elements */}
              <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                <span className="animate-pulse">● REC</span>
              </div>
              <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                2:34
              </div>
            </div>
          </div>
        </div>

        {/* Steps Timeline */}
        <div className="mb-20">
          <div className="grid md:grid-cols-3 gap-8">
            {steps.map((step: any, index: number) => {
              const Icon = stepIcons[index];
              const isActive = activeStep === index;
              
              return (
                <div
                  key={index}
                  className={`relative p-8 rounded-2xl border-2 transition-all duration-300 cursor-pointer transform hover:scale-105 ${
                    isActive 
                      ? 'border-green-500 bg-green-50 shadow-lg' 
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                  }`}
                  onClick={() => setActiveStep(index)}
                >
                  {/* Step Number */}
                  <div className={`absolute -top-4 ${isRTL ? 'right-8' : 'left-8'} w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    isActive ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>

                  <div className="space-y-4">
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                      isActive ? 'bg-green-500' : 'bg-gray-100'
                    }`}>
                      <Icon className={`h-8 w-8 ${isActive ? 'text-white' : 'text-gray-600'}`} />
                    </div>
                    
                    <h3 className="text-xl font-bold text-gray-900">
                      {step.title}
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Connection Line */}
                  {index < steps.length - 1 && (
                    <div className={`hidden md:block absolute top-1/2 ${isRTL ? '-left-4' : '-right-4'} w-8 h-0.5 bg-gray-200`}>
                      <ArrowRight className={`absolute top-1/2 ${isRTL ? '-left-2 rotate-180' : '-right-2'} transform -translate-y-1/2 h-4 w-4 text-gray-400`} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Features Grid */}
        <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              {t('aiBuilder.features.title')}
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('aiBuilder.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature: any, index: number) => {
              const icons = [Brain, MessageSquare, Smartphone, CreditCard, Globe, TrendingUp];
              const Icon = icons[index] || Brain;

              return (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center mb-4">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 mb-2">
                    {typeof feature === 'object' ? feature.title : feature}
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    {typeof feature === 'object' ? feature.description : feature}
                  </p>
                  <div className="w-8 h-1 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mt-4"></div>
                </div>
              );
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Button
            size="lg"
            className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-12 py-6 text-lg font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <Brain className="h-6 w-6 mr-3" />
            {t('aiBuilder.cta')}
          </Button>
          <p className="text-sm text-gray-500 mt-4">
            {t('aiBuilder.guarantee')}
          </p>
        </div>
      </div>
    </section>
  );
}